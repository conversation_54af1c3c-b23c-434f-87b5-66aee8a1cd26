import React from 'react';
import { DashboardActionCard } from '@/components/ui/dashboard-card';
import { 
  Plus, 
  BarChart3, 
  Users, 
  DollarSign, 
  Wallet,
  Settings,
  BookOpen,
  MessageSquare,
  FileText,
  Calendar,
  Award,
  TrendingUp
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface QuickAction {
  label: string;
  href?: string;
  onClick?: () => void;
  icon: React.ReactNode;
  variant?: 'default' | 'outline' | 'ghost';
  disabled?: boolean;
}

interface ModernQuickActionsProps {
  // Stripe connection status for conditional actions
  isStripeConnected?: boolean;
  isConnectingStripe?: boolean;
  upcomingPayoutAmount?: number;
  
  // Action handlers
  onCreateCourse?: () => void;
  onRequestPayout?: () => void;
  onConnectStripe?: () => void;
  
  // Customization
  className?: string;
  variant?: 'full' | 'compact' | 'essential';
}

export const ModernQuickActions: React.FC<ModernQuickActionsProps> = ({
  isStripeConnected = false,
  isConnectingStripe = false,
  upcomingPayoutAmount = 0,
  onCreateCourse,
  onRequestPayout,
  onConnectStripe,
  className,
  variant = 'full',
}) => {
  // Essential actions that are always available
  const essentialActions: QuickAction[] = [
    {
      label: 'Create New Course',
      href: '/teacher/courses/create',
      onClick: onCreateCourse,
      icon: <Plus className="w-4 h-4" />,
      variant: 'default',
    },
    {
      label: 'View Analytics',
      href: '/teacher/analytics',
      icon: <BarChart3 className="w-4 h-4" />,
      variant: 'outline',
    },
    {
      label: 'Manage Students',
      href: '/teacher/students',
      icon: <Users className="w-4 h-4" />,
      variant: 'outline',
    },
    {
      label: 'View Earnings',
      href: '/teacher/earnings',
      icon: <DollarSign className="w-4 h-4" />,
      variant: 'outline',
    },
  ];

  // Additional actions for full variant
  const additionalActions: QuickAction[] = [
    {
      label: 'Course Management',
      href: '/teacher/courses',
      icon: <BookOpen className="w-4 h-4" />,
      variant: 'outline',
    },
    {
      label: 'Messages',
      href: '/teacher/messages',
      icon: <MessageSquare className="w-4 h-4" />,
      variant: 'outline',
    },
    {
      label: 'Reports',
      href: '/teacher/reports',
      icon: <FileText className="w-4 h-4" />,
      variant: 'outline',
    },
    {
      label: 'Settings',
      href: '/teacher/settings',
      icon: <Settings className="w-4 h-4" />,
      variant: 'outline',
    },
  ];

  // Financial actions based on Stripe status
  const financialActions: QuickAction[] = [
    ...(isStripeConnected ? [
      {
        label: 'Request Payout',
        onClick: onRequestPayout,
        icon: <Wallet className="w-4 h-4" />,
        variant: 'outline' as const,
        disabled: !upcomingPayoutAmount || upcomingPayoutAmount <= 0,
      },
    ] : [
      {
        label: 'Connect Stripe',
        onClick: onConnectStripe,
        icon: <Wallet className="w-4 h-4" />,
        variant: 'default' as const,
        disabled: isConnectingStripe,
      },
    ]),
  ];

  // Determine which actions to show based on variant
  const getActionsForVariant = (): QuickAction[] => {
    switch (variant) {
      case 'essential':
        return [...essentialActions.slice(0, 4), ...financialActions];
      case 'compact':
        return [...essentialActions, ...financialActions];
      case 'full':
      default:
        return [...essentialActions, ...financialActions, ...additionalActions];
    }
  };

  const actions = getActionsForVariant();

  return (
    <div className={cn(
      'grid gap-6',
      // Responsive layout based on variant
      variant === 'essential' && 'grid-cols-1',
      variant === 'compact' && 'grid-cols-1 lg:grid-cols-2',
      variant === 'full' && 'grid-cols-1 lg:grid-cols-2 xl:grid-cols-3',
      className
    )}>
      {/* Primary Actions Card */}
      <DashboardActionCard
        id="primary-actions"
        title="Quick Actions"
        description="Common tasks and shortcuts"
        icon={<TrendingUp className="w-4 h-4" />}
        actions={actions.slice(0, Math.ceil(actions.length / (variant === 'full' ? 3 : 2)))}
        className="h-fit"
      />

      {/* Secondary Actions Card (for compact and full variants) */}
      {variant !== 'essential' && actions.length > 4 && (
        <DashboardActionCard
          id="secondary-actions"
          title="Management"
          description="Course and student management"
          icon={<BookOpen className="w-4 h-4" />}
          actions={actions.slice(
            Math.ceil(actions.length / (variant === 'full' ? 3 : 2)),
            Math.ceil(actions.length * 2 / (variant === 'full' ? 3 : 2))
          )}
          className="h-fit"
        />
      )}

      {/* Tertiary Actions Card (for full variant only) */}
      {variant === 'full' && actions.length > 8 && (
        <DashboardActionCard
          id="tertiary-actions"
          title="Settings & Reports"
          description="Configuration and analytics"
          icon={<Settings className="w-4 h-4" />}
          actions={actions.slice(Math.ceil(actions.length * 2 / 3))}
          className="h-fit"
        />
      )}
    </div>
  );
};

// Specialized quick actions for different dashboard sections
interface TeacherQuickActionsProps {
  isStripeConnected?: boolean;
  isConnectingStripe?: boolean;
  upcomingPayoutAmount?: number;
  onCreateCourse?: () => void;
  onRequestPayout?: () => void;
  onConnectStripe?: () => void;
  className?: string;
}

export const TeacherQuickActions: React.FC<TeacherQuickActionsProps> = (props) => {
  return <ModernQuickActions {...props} variant="full" />;
};

export const CompactQuickActions: React.FC<TeacherQuickActionsProps> = (props) => {
  return <ModernQuickActions {...props} variant="compact" />;
};

export const EssentialQuickActions: React.FC<TeacherQuickActionsProps> = (props) => {
  return <ModernQuickActions {...props} variant="essential" />;
};

// Course-specific quick actions
interface CourseQuickActionsProps {
  courseId?: string;
  className?: string;
}

export const CourseQuickActions: React.FC<CourseQuickActionsProps> = ({
  courseId,
  className,
}) => {
  const courseActions: QuickAction[] = [
    {
      label: 'Add Lecture',
      href: courseId ? `/teacher/courses/${courseId}/lecture/create` : '/teacher/courses',
      icon: <Plus className="w-4 h-4" />,
      variant: 'default',
      disabled: !courseId,
    },
    {
      label: 'Edit Course',
      href: courseId ? `/teacher/courses/edit-course/${courseId}` : '/teacher/courses',
      icon: <BookOpen className="w-4 h-4" />,
      variant: 'outline',
      disabled: !courseId,
    },
    {
      label: 'View Students',
      href: '/teacher/students',
      icon: <Users className="w-4 h-4" />,
      variant: 'outline',
    },
    {
      label: 'Course Analytics',
      href: '/teacher/analytics',
      icon: <BarChart3 className="w-4 h-4" />,
      variant: 'outline',
    },
  ];

  return (
    <DashboardActionCard
      id="course-actions"
      title="Course Actions"
      description="Manage your course content"
      icon={<BookOpen className="w-4 h-4" />}
      actions={courseActions}
      className={cn('h-fit', className)}
    />
  );
};

// Student management quick actions
export const StudentQuickActions: React.FC<{ className?: string }> = ({ className }) => {
  const studentActions: QuickAction[] = [
    {
      label: 'View All Students',
      href: '/teacher/students',
      icon: <Users className="w-4 h-4" />,
      variant: 'default',
    },
    {
      label: 'Send Message',
      href: '/teacher/messages',
      icon: <MessageSquare className="w-4 h-4" />,
      variant: 'outline',
    },
    {
      label: 'Student Analytics',
      href: '/teacher/analytics',
      icon: <BarChart3 className="w-4 h-4" />,
      variant: 'outline',
    },
    {
      label: 'Certificates',
      href: '/teacher/certificates',
      icon: <Award className="w-4 h-4" />,
      variant: 'outline',
    },
  ];

  return (
    <DashboardActionCard
      id="student-actions"
      title="Student Management"
      description="Engage with your students"
      icon={<Users className="w-4 h-4" />}
      actions={studentActions}
      className={cn('h-fit', className)}
    />
  );
};
